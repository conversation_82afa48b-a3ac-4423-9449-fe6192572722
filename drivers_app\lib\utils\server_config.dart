import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../services/auth_service.dart';

class ServerConfig {
  static const String SERVER_URL_KEY = 'http://192.168.117.143:3001';
  static const String DEFAULT_SERVER_URL = DriverAuthService.baseUrl;
  
  // Get the current server URL
  static Future<String> getServerUrl() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(SERVER_URL_KEY) ?? DEFAULT_SERVER_URL;
  }
  
  // Update the server URL
  static Future<bool> setServerUrl(String url) async {
    try {
      // Validate URL format
      final uri = Uri.parse(url);
      if (!uri.isAbsolute || (!url.startsWith('http://') && !url.startsWith('https://'))) {
        return false;
      }
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(SERVER_URL_KEY, url);
      return true;
    } catch (e) {
      print('Error setting server URL: $e');
      return false;
    }
  }
  
  // Show server configuration dialog
  static Future<void> showServerConfigDialog(BuildContext context) async {
    final currentUrl = await getServerUrl();
    final controller = TextEditingController(text: currentUrl);
    String? errorMessage;
    
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (ctx) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text('إعدادات الخادم', textAlign: TextAlign.center),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: controller,
                    decoration: InputDecoration(
                      labelText: 'عنوان الخادم',
                      hintText: 'http://192.168.x.x:3001',
                      errorText: errorMessage,
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.url,
                  ),
                  SizedBox(height: 20),
                  Text(
                    'يجب إدخال عنوان الخادم بالصيغة الصحيحة مع رقم المنفذ',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    final url = controller.text.trim();
                    
                    try {
                      // Validate URL format
                      final uri = Uri.parse(url);
                      if (!uri.isAbsolute || (!url.startsWith('http://') && !url.startsWith('https://'))) {
                        setState(() {
                          errorMessage = 'يرجى إدخال عنوان URL صالح';
                        });
                        return;
                      }
                      
                      // Test connection to new URL
                      try {
                        final response = await http.get(
                          Uri.parse('$url/driver-auth/login'),
                          headers: {'Accept': 'application/json'},
                        ).timeout(Duration(seconds: 5));
                        
                        // Any response means we connected
                        await setServerUrl(url);
                        Navigator.of(context).pop(true);
                        
                        // Show success message
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('تم تحديث عنوان الخادم بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      } catch (e) {
                        setState(() {
                          errorMessage = 'لا يمكن الاتصال بالخادم: ${e.toString()}';
                        });
                      }
                    } catch (e) {
                      setState(() {
                        errorMessage = 'عنوان URL غير صالح';
                      });
                    }
                  },
                  child: Text('حفظ'),
                ),
              ],
            );
          },
        );
      },
    );
  }
  
  // Get detected local IP addresses
  static Future<List<String>> getLocalIpAddresses() async {
    try {
      final interfaces = await NetworkInterface.list(
        includeLoopback: false,
        type: InternetAddressType.IPv4,
      );
      
      final addresses = <String>[];
      for (var interface in interfaces) {
        for (var addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4) {
            addresses.add(addr.address);
          }
        }
      }
      return addresses;
    } catch (e) {
      print('Error getting local IP: $e');
      return [];
    }
  }
} 