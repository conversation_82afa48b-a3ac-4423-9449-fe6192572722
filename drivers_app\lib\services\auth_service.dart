import 'dart:convert';
import 'dart:io';
import 'dart:async';  // Added for TimeoutException
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/server_config.dart';

class DriverAuthService {
  // Default URL - will be overridden by ServerConfig
  static const String baseUrl = 'http://192.168.117.143:3001';
  static const String loginEndpoint = '/driver-auth/login';
  static const String logoutEndpoint = '/driver-auth/logout';
  static const String refreshTokenEndpoint = '/driver-auth/refresh-token';

  // Store for tokens and user data
  static const String ACCESS_TOKEN_KEY = 'driver_access_token';
  static const String REFRESH_TOKEN_KEY = 'driver_refresh_token';
  static const String USER_DATA_KEY = 'driver_user_data';

  static Future<bool> testConnection() async {
    try {
      // Get the current server URL from configuration
      final serverUrl = await ServerConfig.getServerUrl();
      print('Testing connection to $serverUrl');
      
      // Try the health check endpoint first as it should always respond without authentication
      final healthCheckUrl = '$serverUrl/health-check';
      print('Health check URL: $healthCheckUrl');
      
      try {
        final healthResponse = await http.get(
          Uri.parse(healthCheckUrl),
          headers: {'Accept': 'application/json'}
        ).timeout(const Duration(seconds: 8));
        
        print('Health check response: ${healthResponse.statusCode} - ${healthResponse.body}');
        
        // If health check works, we're good
        if (healthResponse.statusCode >= 200 && healthResponse.statusCode < 400) {
          return true;
        }
      } catch (e) {
        // If health check fails, try login endpoint as fallback
        print('Health check failed, trying login endpoint: $e');
      }
      
      // Fallback - try the login endpoint which should respond even without auth
      final loginUrl = '$serverUrl$loginEndpoint';
      print('Connection test URL: $loginUrl');
      
      final response = await http.get(
        Uri.parse(loginUrl), 
        headers: {'Accept': 'application/json'}
      ).timeout(const Duration(seconds: 10));
      
      print('Connection test response: ${response.statusCode} - ${response.body}');
      
      // For debugging, let's print detailed information
      print('Response headers: ${response.headers}');
      
      // Any status code means we reached the server
      return response.statusCode != 0; 
    } on SocketException catch (e) {
      print('Connection test socket error: $e');
      print('This indicates a network connectivity issue or incorrect server address');
      return false;
    } on http.ClientException catch (e) {
      print('Connection test HTTP client error: $e');
      print('This may indicate an issue with the server URL format or reachability');
      return false;
    } on TimeoutException catch (e) {
      print('Connection test timeout: $e');
      print('The server is taking too long to respond');
      return false;
    } catch (e) {
      print('Connection test unknown error: $e');
      print('Error type: ${e.runtimeType}');
      return false;
    }
  }

  // Login method - uses the configurable server URL
  static Future<Map<String, dynamic>> login(String username, String password) async {
    try {
      print('Attempting to login with username: $username');
      
      // Test connection first
      final isConnected = await testConnection();
      if (!isConnected) {
        print('Cannot connect to the server during login');
        return {
          'success': false,
          'message': 'لا يمكن الاتصال بالخادم. تحقق من اتصالك بالإنترنت وحاول مرة أخرى.'
        };
      }

      // Get the current server URL from configuration
      final serverUrl = await ServerConfig.getServerUrl();
      final url = '$serverUrl$loginEndpoint';
      
      print('Login request to: $url');
      
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'username': username,
          'password': password,
        }),
      ).timeout(const Duration(seconds: 15));  // Longer timeout for login

      print('Login response status: ${response.statusCode}');
      print('Login response body: ${response.body}');
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        
        print('Response data structure: ${responseData.keys.join(', ')}');
        
        // Check that we have the expected data
        if (!responseData.containsKey('success') || 
            !responseData.containsKey('tokens') || 
            !responseData['tokens'].containsKey('accessToken') ||
            !responseData.containsKey('user')) {
          print('WARNING: Server responded with 200 but missing critical data:');
          print('Has success flag: ${responseData.containsKey('success')}');
          print('Has tokens: ${responseData.containsKey('tokens')}');
          print('Has accessToken: ${responseData.containsKey('tokens') ? responseData['tokens'].containsKey('accessToken') : false}');
          print('Has user data: ${responseData.containsKey('user')}');
          
          return {
            'success': false,
            'message': 'استجابة غير صالحة من الخادم'
          };
        }
        
        final accessToken = responseData['tokens']['accessToken'];
        final refreshToken = responseData['tokens']['refreshToken'];
        final userData = responseData['user'];

        // Store tokens and user data
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(ACCESS_TOKEN_KEY, accessToken);
        await prefs.setString(REFRESH_TOKEN_KEY, refreshToken);
        await prefs.setString(USER_DATA_KEY, json.encode(userData));

        return {
          'success': true,
          'user': userData,
          'accessToken': accessToken,
          'refreshToken': refreshToken,
        };
      } else {
        // Parse the error message from the response if possible
        String errorMessage;
        try {
          final errorData = json.decode(response.body);
          errorMessage = errorData['message'] ?? errorData['response']?['message'] ?? 'حدث خطأ أثناء تسجيل الدخول';
        } catch (e) {
          errorMessage = 'حدث خطأ أثناء تسجيل الدخول';
        }

        return {
          'success': false,
          'message': errorMessage,
          'statusCode': response.statusCode
        };
      }
    } on SocketException catch (e) {
      print('Login socket error: $e');
      return {
        'success': false,
        'message': 'لا يمكن الاتصال بالخادم. تحقق من اتصالك بالإنترنت.'
      };
    } on TimeoutException catch (e) {
      print('Login timeout error: $e');
      return {
        'success': false,
        'message': 'انتهت مهلة الاتصال بالخادم. حاول مرة أخرى لاحقًا.'
      };
    } on FormatException catch (e) {
      print('Login format error: $e');
      return {
        'success': false,
        'message': 'حدث خطأ في معالجة البيانات. تأكد من أن الخادم يعمل بشكل صحيح.'
      };
    } catch (e) {
      print('Login unknown error: $e (${e.runtimeType})');
      return {
        'success': false,
        'message': 'حدث خطأ غير متوقع: $e'
      };
    }
  }

  // Remaining methods also need to use the configurable URL
  static Future<Map<String, dynamic>> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final accessToken = prefs.getString(ACCESS_TOKEN_KEY);
      
      if (accessToken != null) {
        // Get the current server URL
        final serverUrl = await ServerConfig.getServerUrl();
        final url = '$serverUrl$logoutEndpoint';
        
        try {
          await http.post(
            Uri.parse(url),
            headers: {
              'Authorization': 'Bearer $accessToken',
              'Content-Type': 'application/json',
            },
          );
        } catch (e) {
          print('Error calling logout API: $e');
          // Continue with local logout even if API call fails
        }
      }
      
      // Clear local storage
      await prefs.remove(ACCESS_TOKEN_KEY);
      await prefs.remove(REFRESH_TOKEN_KEY);
      await prefs.remove(USER_DATA_KEY);
      
      return {
        'success': true,
      };
    } catch (e) {
      print('Logout error: $e');
      return {
        'success': false,
        'message': 'حدث خطأ أثناء تسجيل الخروج: $e'
      };
    }
  }
  
  static Future<Map<String, dynamic>> refreshToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final refreshToken = prefs.getString(REFRESH_TOKEN_KEY);
      
      if (refreshToken == null) {
        return {
          'success': false,
          'message': 'لا يوجد رمز تحديث'
        };
      }
      
      // Get the current server URL
      final serverUrl = await ServerConfig.getServerUrl();
      final url = '$serverUrl$refreshTokenEndpoint';
      
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'refreshToken': refreshToken,
        }),
      );
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final newAccessToken = responseData['accessToken'];
        final newRefreshToken = responseData['refreshToken'];
        
        // Update stored tokens
        await prefs.setString(ACCESS_TOKEN_KEY, newAccessToken);
        await prefs.setString(REFRESH_TOKEN_KEY, newRefreshToken);
        
        return {
          'success': true,
          'accessToken': newAccessToken,
          'refreshToken': newRefreshToken,
        };
      } else {
        // Token refresh failed, user needs to login again
        await prefs.remove(ACCESS_TOKEN_KEY);
        await prefs.remove(REFRESH_TOKEN_KEY);
        await prefs.remove(USER_DATA_KEY);
        
        return {
          'success': false,
          'message': 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى'
        };
      }
    } catch (e) {
      print('Token refresh error: $e');
      return {
        'success': false,
        'message': 'حدث خطأ أثناء تحديث الجلسة: $e'
      };
    }
  }
  
  static Future<Map<String, dynamic>?> getSavedAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final accessToken = prefs.getString(ACCESS_TOKEN_KEY);
      final refreshToken = prefs.getString(REFRESH_TOKEN_KEY);
      final userDataString = prefs.getString(USER_DATA_KEY);
      
      if (accessToken == null || refreshToken == null || userDataString == null) {
        return null;
      }
      
      try {
        final userData = json.decode(userDataString);
        
        return {
          'accessToken': accessToken,
          'refreshToken': refreshToken,
          'user': userData
        };
      } catch (e) {
        print('Error parsing stored user data: $e');
        return null;
      }
    } catch (e) {
      print('Error retrieving saved auth: $e');
      return null;
    }
  }
  
  // Helper method for checking saved authentication data
  static Future<Map<String, dynamic>?> checkSavedAuth() async {
    return await getSavedAuth();
  }
} 