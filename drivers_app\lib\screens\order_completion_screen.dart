import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/api_client.dart';

class OrderCompletionScreen extends StatefulWidget {
  final Map<String, dynamic> order;

  const OrderCompletionScreen({super.key, required this.order});

  @override
  State<OrderCompletionScreen> createState() => _OrderCompletionScreenState();
}

class _OrderCompletionScreenState extends State<OrderCompletionScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  int _currentStep = 0;
  bool _isLoading = false;
  
  final List<OrderStep> _steps = [
    OrderStep(
      title: 'الذهاب إلى المطعم',
      description: 'توجه إلى المطعم لاستلام الطلب',
      icon: Icons.restaurant,
      color: Colors.orange,
      status: 2, // Out for delivery to restaurant
    ),
    OrderStep(
      title: 'استلام الطلب',
      description: 'استلم الطلب من المطعم وتأكد من اكتماله',
      icon: Icons.shopping_bag,
      color: Colors.blue,
      status: 2, // Still out for delivery
    ),
    OrderStep(
      title: 'الذهاب إلى العميل',
      description: 'توجه إلى عنوان العميل لتسليم الطلب',
      icon: Icons.directions_car,
      color: Colors.purple,
      status: 2, // Still out for delivery
    ),
    OrderStep(
      title: 'تسليم الطلب',
      description: 'سلم الطلب للعميل واستلم المبلغ',
      icon: Icons.handshake,
      color: Colors.green,
      status: 3, // Delivered
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
    
    // Determine current step based on order status
    _determineCurrentStep();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _determineCurrentStep() {
    final status = widget.order['Status'] ?? 2;
    if (status == 2) {
      _currentStep = 0; // Out for delivery - start from going to restaurant
    } else if (status == 3) {
      _currentStep = 4; // Delivered - all steps completed
    }
  }

  Future<void> _updateOrderStatus(int newStatus) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiClient = ApiClient();
      await apiClient.put('/drivers/app/order-status/${widget.order['OrderID']}', {
        'status': newStatus,
      });

      if (newStatus == 3) {
        // Order completed
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تسليم الطلب بنجاح!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(true); // Return true to indicate completion
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحديث حالة الطلب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يمكن إجراء المكالمة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final orderId = widget.order['OrderID']?.toString() ?? 'N/A';
    final restaurantName = widget.order['Cart']?['Restaurant']?['RestaurantName'] ?? 'مطعم';
    final customerName = '${widget.order['Customer']?['FirstName'] ?? ''} ${widget.order['Customer']?['LastName'] ?? ''}';
    final customerPhone = widget.order['Customer']?['PhoneNumber'] ?? '';
    final total = widget.order['Cart']?['CartTotalPrice']?.toDouble() ?? 0.0;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.purple[50]!,
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.arrow_back),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'طلب #$orderId',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            Text(
                              restaurantName,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Order Info Card
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.purple.withValues(alpha: 0.1),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.purple[50],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.person,
                              color: Colors.purple[600],
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'العميل: $customerName',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                if (customerPhone.isNotEmpty)
                                  GestureDetector(
                                    onTap: () => _makePhoneCall(customerPhone),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.green[50],
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.phone,
                                            color: Colors.green[600],
                                            size: 16,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            customerPhone,
                                            style: TextStyle(
                                              color: Colors.green[600],
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                '${total.toStringAsFixed(2)} د.ل',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.purple[600],
                                ),
                              ),
                              Text(
                                'إجمالي الطلب',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Steps
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    itemCount: _steps.length,
                    itemBuilder: (context, index) {
                      return _buildStepCard(index);
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStepCard(int index) {
    final step = _steps[index];
    final isCompleted = index < _currentStep;
    final isCurrent = index == _currentStep;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          // Step indicator
          Column(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: isCompleted
                      ? Colors.green
                      : isCurrent
                          ? step.color
                          : Colors.grey[300],
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: isCurrent ? [
                    BoxShadow(
                      color: step.color.withValues(alpha: 0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ] : null,
                ),
                child: Icon(
                  isCompleted ? Icons.check : step.icon,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              if (index < _steps.length - 1)
                Container(
                  width: 2,
                  height: 40,
                  color: isCompleted ? Colors.green : Colors.grey[300],
                ),
            ],
          ),

          const SizedBox(width: 16),

          // Step content
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: isCurrent ? Border.all(
                  color: step.color,
                  width: 2,
                ) : null,
                boxShadow: [
                  BoxShadow(
                    color: Colors.purple.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          step.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isCurrent ? step.color : Colors.black87,
                          ),
                        ),
                      ),
                      if (isCompleted)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'مكتمل',
                            style: TextStyle(
                              color: Colors.green[800],
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    step.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),

                  if (isCurrent) ...[
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : () => _completeStep(index),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: step.color,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                _getStepButtonText(index),
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getStepButtonText(int index) {
    switch (index) {
      case 0:
        return 'وصلت إلى المطعم';
      case 1:
        return 'استلمت الطلب';
      case 2:
        return 'وصلت إلى العميل';
      case 3:
        return 'تم تسليم الطلب';
      default:
        return 'التالي';
    }
  }

  void _completeStep(int index) {
    setState(() {
      _currentStep = index + 1;
    });

    if (index == _steps.length - 1) {
      // Last step - mark order as delivered
      _updateOrderStatus(_steps[index].status);
    }
  }
}

class OrderStep {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final int status;

  OrderStep({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.status,
  });
}
