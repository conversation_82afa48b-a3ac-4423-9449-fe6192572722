const express = require('express');
const router = express.Router();
const driverController = require('../controllers/driverController');
const { authenticateDriverToken } = require('../middleware/driverAuthMiddleware');

// Admin routes (no auth required for admin operations)
router.get('/', driverController.getAllDrivers);
router.post('/', driverController.createDriver);
router.get('/search', driverController.searchDrivers);
router.get('/:id', driverController.getDriverById);
router.put('/:id', driverController.editDriver);
router.put('/:id/status', driverController.updateDriverStatus);
router.patch('/:id/activate', driverController.toggleDriverActivation);
router.post('/receive-wallet', driverController.receiveWallet);
router.delete('/:id', driverController.deleteDriver);

router.put('/news', driverController.updateDriverNews);
router.get('/news', driverController.getDriverNews);
router.get('/profit-percent', driverController.getCompanyProfitPercent);
router.put('/profit-percent', driverController.updateCompanyProfitPercent);

router.get('/:id/orders', driverController.getDriverOrders);
router.get('/invoices/all', driverController.getInvoices);
router.get('/:id/invoices', driverController.getDriverInvoices);

// Driver app routes (require authentication)
router.get('/app/available-orders', authenticateDriverToken, driverController.getAvailableOrders);
router.get('/app/my-orders', authenticateDriverToken, driverController.getMyOrders);
router.post('/app/accept-order/:orderId', authenticateDriverToken, driverController.acceptOrder);
router.get('/app/stats', authenticateDriverToken, driverController.getDriverStats);
router.get('/app/wallet', authenticateDriverToken, driverController.getDriverWallet);
router.get('/app/moderate', authenticateDriverToken, driverController.getDriverModerate);
router.put('/app/order-status/:orderId', authenticateDriverToken, driverController.updateOrderStatus);

module.exports = router;
