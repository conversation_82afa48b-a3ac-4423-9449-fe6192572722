import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/server_config.dart';
import '../services/auth_service.dart';

class ApiClient {
  // Will be dynamically retrieved from ServerConfig
  String baseUrl = DriverAuthService.baseUrl;
  
  // Token keys
  static const String ACCESS_TOKEN_KEY = DriverAuthService.ACCESS_TOKEN_KEY;
  static const String REFRESH_TOKEN_KEY = DriverAuthService.REFRESH_TOKEN_KEY;
  
  // Headers with authentication
  Future<Map<String, String>> _getAuthHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(ACCESS_TOKEN_KEY);

    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      // For testing, use placeholder token that backend expects
      'Authorization': 'Bearer ${token ?? "placeholder_token"}',
    };
  }
  
  // Initialize with current server URL
  Future<void> _initBaseUrl() async {
    baseUrl = await ServerConfig.getServerUrl();
    print('API Client initialized with URL: $baseUrl');
  }
  
  // GET request with authentication
  Future<dynamic> get(String endpoint) async {
    await _initBaseUrl(); // Ensure we have the latest URL
    
    try {
      final url = '$baseUrl$endpoint';
      final headers = await _getAuthHeaders();

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      ).timeout(const Duration(seconds: 15));

      return _handleResponse(response);
    } catch (e) {
      print('API GET error: $e');
      rethrow;
    }
  }
  
  // POST request with authentication
  Future<dynamic> post(String endpoint, Map<String, dynamic> data) async {
    await _initBaseUrl(); // Ensure we have the latest URL
    
    try {
      final url = '$baseUrl$endpoint';
      print('POST request to: $url');
      print('POST request body: $data');
      
      final response = await http.post(
        Uri.parse(url),
        headers: await _getAuthHeaders(),
        body: json.encode(data),
      ).timeout(const Duration(seconds: 15));
      
      print('POST response status: ${response.statusCode}');
      print('POST response body: ${response.body}');
      
      return _handleResponse(response);
    } catch (e) {
      print('API POST error: $e');
      rethrow;
    }
  }
  
  // PUT request with authentication
  Future<dynamic> put(String endpoint, Map<String, dynamic> data) async {
    await _initBaseUrl(); // Ensure we have the latest URL
    
    try {
      final url = '$baseUrl$endpoint';
      print('PUT request to: $url');
      
      final response = await http.put(
        Uri.parse(url),
        headers: await _getAuthHeaders(),
        body: json.encode(data),
      ).timeout(const Duration(seconds: 15));
      
      print('PUT response status: ${response.statusCode}');
      
      return _handleResponse(response);
    } catch (e) {
      print('API PUT error: $e');
      rethrow;
    }
  }
  
  // DELETE request with authentication
  Future<dynamic> delete(String endpoint) async {
    await _initBaseUrl(); // Ensure we have the latest URL
    
    try {
      final url = '$baseUrl$endpoint';
      print('DELETE request to: $url');
      
      final response = await http.delete(
        Uri.parse(url),
        headers: await _getAuthHeaders(),
      ).timeout(const Duration(seconds: 15));
      
      print('DELETE response status: ${response.statusCode}');
      
      return _handleResponse(response);
    } catch (e) {
      print('API DELETE error: $e');
      rethrow;
    }
  }
  
  // Handle API response
  dynamic _handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      if (response.body.isEmpty) return {};
      
      try {
        return json.decode(response.body);
      } catch (e) {
        print('Error decoding response: $e');
        throw FormatException('Invalid response format');
      }
    } else if (response.statusCode == 401) {
      // Unauthorized - token might be expired
      throw UnauthorizedException('Unauthorized access. Please login again.');
    } else {
      String message = 'Request failed with status: ${response.statusCode}';
      
      try {
        final errorData = json.decode(response.body);
        if (errorData.containsKey('message')) {
          message = errorData['message'];
        }
      } catch (_) {}
      
      throw ApiException(message, response.statusCode);
    }
  }
}

// Custom exceptions for better error handling
class ApiException implements Exception {
  final String message;
  final int statusCode;
  
  ApiException(this.message, this.statusCode);
  
  @override
  String toString() => 'ApiException: $message (Status code: $statusCode)';
}

class UnauthorizedException implements Exception {
  final String message;
  
  UnauthorizedException(this.message);
  
  @override
  String toString() => 'UnauthorizedException: $message';
} 