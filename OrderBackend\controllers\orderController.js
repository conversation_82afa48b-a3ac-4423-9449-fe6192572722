const { Sequelize , where, Op } = require('sequelize');
const sequelize = require('../config/database');
const db = require('../models/init-models')(sequelize);
const { Order, Cart, ProductCart, Product, IngredientUsage, Ingredient, Restaurant, Customer, Restaurant_Category_Product } = db;
const { getPagination, getPagingData } = require('../utils/pagination');

// Helper function to calculate distance between two points
function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
        Math.cos(φ1) * Math.cos(φ2) *
        Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
}



exports.getAllOrders = async(req, res) => {
    try {
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        const orders = await Order.findAndCountAll({
            limit,
            offset,
            order: [['OrderDate', 'DESC']],
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    attributes: ['TotalPrice'],
                },
                {
                    model: db.Driver,
                    as: 'Driver',
                    attributes: ['FirstName', 'LastName'],
                    required: false
                }
            ]
        });

        const formattedOrders = orders.rows.map(async order => {
            const plainOrder = order.get({ plain: true });

            const {
                StartPointLatitude,
                StartPointLongitude,
                EndPointLatitude,
                EndPointLongitude,
                ...orderData
            } = plainOrder;

            let restaurantName = null;
            let cartTotalPrice = null;
            let driverFirstName = null;
            let driverLastName = null;

            if (order.CartID) {
                const cart = await Cart.findByPk(order.CartID);
                if (cart) {
                    cartTotalPrice = cart.TotalPrice;
                    if (cart.RestaurantID) {
                        const restaurant = await Restaurant.findByPk(cart.RestaurantID);
                        if (restaurant) {
                            restaurantName = restaurant.Name;
                        }
                    }
                }
            }

            if (order.Driver) {
                driverFirstName = order.Driver.FirstName;
                driverLastName = order.Driver.LastName;
            }

            let distanceInMeters = null;
            if (
                StartPointLatitude != null &&
                StartPointLongitude != null &&
                EndPointLatitude != null &&
                EndPointLongitude != null
            ) {
                distanceInMeters = calculateDistance(
                    StartPointLatitude,
                    StartPointLongitude,
                    EndPointLatitude,
                    EndPointLongitude
                );
            }

            return {
                ...orderData,
                RestaurantName: restaurantName,
                CartTotalPrice: cartTotalPrice,
                DriverFirstName: driverFirstName,
                DriverLastName: driverLastName,
                DistanceInMeters: distanceInMeters
            };
        });

        const resolvedFormattedOrders = await Promise.all(formattedOrders);

        const response = getPagingData({ rows: resolvedFormattedOrders, count: orders.count }, page, limit);
        res.status(200).json(response);

    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching orders',
            error: error.message
        });
    }
};


exports.getOrderDetails = async(req, res) => {
    try {
        const order = await Order.findByPk(req.params.orderId, {
            include: [{
                    model: Cart,
                    as: 'Cart',
                    include: [{
                        model: ProductCart,
                        as: 'ProductCarts',
                        include: [{
                                model: Product,
                                as: 'Product',
                                attributes: ['ProductName', 'Price']
                            },
                            {
                                model: IngredientUsage,
                                as: 'IngredientUsages',
                                include: [{
                                    model: Ingredient,
                                    as: 'Ingredient',
                                    attributes: ['IngredientName']
                                }]
                            }
                        ]
                    }]
                },
                {
                    model: db.Customer,
                    as: 'Customer',
                    attributes: ['CustomerID', 'PhoneNum']
                },
                {
                    model: db.Driver,
                    as: 'Driver',
                    attributes: ['DriverID', 'FirstName', 'LastName', 'PhoneNumber']
                },
                {
                    model: db.Invoice,
                    as: 'Invoices',
                    required: false,
                    attributes: ['InvoiceID']
                }
            ]
        });

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        let restaurant = null;
        if (order.Cart) {
            restaurant = await Restaurant.findByPk(order.Cart.RestaurantID, {
                attributes: ['RestaurantID', 'Name', 'Image', 'City', 'Address']
            });
        }

        const orderData = order.get({ plain: true });

        const formattedProducts = orderData.Cart ? orderData.Cart.ProductCarts.map(pc => ({
            ProductName: pc.Product.ProductName,
            Price: pc.Product.Price,
            Quantity: pc.Quantity,
            Ingredients: pc.IngredientUsages.map(iu => iu.Ingredient.IngredientName)
        })) : [];

        const formattedOrder = {
            ...orderData,
            Cart: {
                ...orderData.Cart,
                Restaurant: restaurant,
                Products: formattedProducts
            },
            Location: {
                StartPoint: {
                    Latitude: orderData.StartPointLatitude,
                    Longitude: orderData.StartPointLongitude
                },
                EndPoint: {
                    Latitude: orderData.EndPointLatitude,
                    Longitude: orderData.EndPointLongitude
                }
            },
            DistanceInMeters: calculateDistance(
                orderData.StartPointLatitude,
                orderData.StartPointLongitude,
                orderData.EndPointLatitude,
                orderData.EndPointLongitude
            ),
            InvoiceID: order.Invoice ? order.Invoice.InvoiceID : null
        };

        delete formattedOrder.Cart.ProductCarts;
        delete formattedOrder.StartPointLatitude;
        delete formattedOrder.StartPointLongitude;
        delete formattedOrder.EndPointLatitude;
        delete formattedOrder.EndPointLongitude;

        res.json(formattedOrder);
    } catch (error) {
        console.error('Error in getOrderDetails:', error);
        res.status(500).json({ message: 'Error fetching order details' });
    }
};

exports.getOrderCart = async(req, res) => {
    try {
        const order = await Order.findByPk(req.params.orderId);
        if (!order) {
            return res.status(404).json({ message: 'لم يتم العثور على الطلب' });
        }

        const cart = await Cart.findByPk(order.CartID, {
            attributes: ['CartID', 'RestaurantID', 'CustomerID', 'TotalPrice', 'CartDate'],
            include: [{
                model: ProductCart,
                as: 'ProductCarts',
                include: [{
                        model: Product,
                        as: 'Product',
                        attributes: ['ProductName', 'Price']
                    },
                    {
                        model: IngredientUsage,
                        as: 'IngredientUsages',
                        include: [{
                            model: Ingredient,
                            as: 'Ingredient',
                            attributes: ['IngredientName']
                        }]
                    }
                ]
            }]
        });

        if (!cart) {
            return res.status(404).json({ message: 'لم يتم العثور على السلة' });
        }

        const response = {
            cartDetails: {
                cartId: cart.CartID,
                restaurantId: cart.RestaurantID,
                customerId: cart.CustomerID,
                totalPrice: cart.TotalPrice,
                cartDate: cart.CartDate
            },
            products: cart.ProductCarts.map(pc => ({
                productName: pc.Product.ProductName,
                price: pc.Product.Price,
                quantity: pc.Quantity,
                ingredients: pc.IngredientUsages.map(iu => ({
                    name: iu.Ingredient.IngredientName,
                    quantity: iu.Quantity
                }))
            }))
        };

        res.json(response);
    } catch (error) {
        console.error('Error in getOrderCart:', error);
        res.status(500).json({ message: error.message + 'حدث خطأ أثناء جلب تفاصيل السلة' });
    }
};

exports.getRestaurantOrders = async(req, res) => {
    try {
        const { restaurantId } = req.params;
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        console.log(`getRestaurantOrders: Fetching orders for restaurant ID: ${restaurantId}`);

        // First get all cart IDs for this restaurant
        const restaurantCarts = await Cart.findAll({
            where: { RestaurantID: Number(restaurantId) },
            attributes: ['CartID']
        });

        const cartIds = restaurantCarts.map(cart => cart.CartID);
        console.log(`getRestaurantOrders: Found ${cartIds.length} carts for restaurant`);

        if (cartIds.length === 0) {
            console.log(`getRestaurantOrders: No carts found for restaurant ${restaurantId}`);
            const response = getPagingData({ rows: [], count: 0 }, page, limit);
            return res.status(200).json(response);
        }

        // Now get orders with pagination, exactly like getAllOrders
        const orders = await Order.findAndCountAll({
            where: {
                CartID: cartIds
            },
            limit,
            offset,
            order: [['OrderDate', 'DESC']],
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    attributes: ['TotalPrice'],
                },
                {
                    model: db.Driver,
                    as: 'Driver',
                    attributes: ['FirstName', 'LastName'],
                    required: false
                }
            ]
        });

        console.log(`getRestaurantOrders: Found ${orders.count} total orders`);

        const formattedOrders = orders.rows.map(async order => {
            const plainOrder = order.get({ plain: true });

            const {
                StartPointLatitude,
                StartPointLongitude,
                EndPointLatitude,
                EndPointLongitude,
                ...orderData
            } = plainOrder;

            let restaurantName = null;
            let cartTotalPrice = null;
            let driverFirstName = null;
            let driverLastName = null;
            let customerPhoneNum = null;
            let productCarts = [];

            if (order.CartID) {
                const cart = await Cart.findByPk(order.CartID, {
                    include: [
                        {
                            model: ProductCart,
                            as: 'ProductCarts',
                            include: [
                                {
                                    model: Product,
                                    as: 'Product',
                                    attributes: ['ProductID', 'ProductName', 'Price', 'Image']
                                },
                                {
                                    model: IngredientUsage,
                                    as: 'IngredientUsages',
                                    include: [
                                        {
                                            model: Ingredient,
                                            as: 'Ingredient',
                                            attributes: ['IngredientID', 'IngredientName']
                                        }
                                    ],
                                    required: false
                                }
                            ]
                        }
                    ]
                });

                if (cart) {
                    cartTotalPrice = cart.TotalPrice;
                    productCarts = cart.ProductCarts || [];

                    if (cart.RestaurantID) {
                        const restaurant = await Restaurant.findByPk(cart.RestaurantID);
                        if (restaurant) {
                            restaurantName = restaurant.Name;
                        }
                    }
                }
            }

            if (order.Driver) {
                driverFirstName = order.Driver.FirstName;
                driverLastName = order.Driver.LastName;
            }

            // Get customer phone number
            if (order.CustomerID) {
                const customer = await Customer.findByPk(order.CustomerID, {
                    attributes: ['PhoneNum']
                });
                if (customer) {
                    customerPhoneNum = customer.PhoneNum;
                }
            }

            let distanceInMeters = null;
            if (
                StartPointLatitude != null &&
                StartPointLongitude != null &&
                EndPointLatitude != null &&
                EndPointLongitude != null
            ) {
                distanceInMeters = calculateDistance(
                    StartPointLatitude,
                    StartPointLongitude,
                    EndPointLatitude,
                    EndPointLongitude
                );
            }

            return {
                ...orderData,
                RestaurantName: restaurantName,
                CartTotalPrice: cartTotalPrice,
                DriverFirstName: driverFirstName,
                DriverLastName: driverLastName,
                CustomerPhoneNum: customerPhoneNum,
                DistanceInMeters: distanceInMeters,
                Products: productCarts
            };
        });

        const resolvedFormattedOrders = await Promise.all(formattedOrders);

        const response = getPagingData({ rows: resolvedFormattedOrders, count: orders.count }, page, limit);
        res.status(200).json(response);

    } catch (error) {
        console.error('Error in getRestaurantOrders:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching restaurant orders',
            error: error.message
        });
    }
};




exports.getDriverOrders = async(req, res) => {
    try {
        const orders = await Order.findAll({
            where: { DriverID: req.params.driverId },
            include: [{
                association: 'Cart',
                attributes: ['CartID', 'TotalPrice', 'CartDate']
            }]
        });
        res.json(orders);
    } catch (error) {
        console.error('Error in getDriverOrders:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء جلب طلبات السائق' });
    }
};

exports.editOrder = async(req, res) => {
    try {
        const order = await Order.findByPk(req.params.orderId);

        if (!order) {
            return res.status(404).json({ message: 'لم يتم العثور على الطلب' });
        }

        await order.update(req.body);

        const updatedOrder = await Order.findByPk(req.params.orderId, {
            include: [{
                association: 'Driver',
                attributes: ['FirstName', 'LastName'],
                required: false
            }]
        });

        res.json(updatedOrder);
    } catch (error) {
        console.error('Error in editOrder:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء تعديل الطلب' });
    }
};

// Create a new order
exports.createOrder = async (req, res) => {
    try {
        console.log('Creating order with data:', JSON.stringify(req.body, null, 2));

        const {
            customerId,
            items,
            address,
            paymentMethod,
            note,
            endPointLatitude,
            endPointLongitude
        } = req.body;

        // Validate required fields
        if (!customerId || !items || !Array.isArray(items) || items.length === 0) {
            console.log('Validation failed: Missing customerId or items');
            return res.status(400).json({
                success: false,
                message: 'Customer ID and items are required'
            });
        }

        console.log(`Processing order for customer ${customerId} with ${items.length} items`);

        // Get the first item to determine restaurant
        const firstItem = items[0];
        let restaurantId = parseInt(firstItem.restaurantId);
        console.log(`Restaurant ID from first item: ${restaurantId}`);

        // If restaurant ID is 0 or invalid, try to get it from the product
        if (!restaurantId || restaurantId === 0 || isNaN(restaurantId)) {
            console.log('Restaurant ID is invalid, trying to get from product...');

            // Get the product to find its restaurant
            const product = await Product.findOne({
                where: { ProductID: firstItem.productId },
                include: [{
                    model: Restaurant_Category_Product,
                    as: 'Restaurant_Category_Products',
                    attributes: ['RestaurantID']
                }]
            });

            if (product && product.Restaurant_Category_Products && product.Restaurant_Category_Products.length > 0) {
                restaurantId = product.Restaurant_Category_Products[0].RestaurantID;
                console.log(`Found restaurant ID from product: ${restaurantId}`);
            } else {
                console.log('Could not determine restaurant ID from product');
                return res.status(400).json({
                    success: false,
                    message: 'Could not determine restaurant for this order. Please try again.'
                });
            }
        }

        // Validate all items are from the same restaurant (if they have restaurant IDs)
        const itemsWithRestaurantIds = items.filter(item => item.restaurantId && parseInt(item.restaurantId) !== 0);
        if (itemsWithRestaurantIds.length > 0) {
            const allSameRestaurant = itemsWithRestaurantIds.every(item => parseInt(item.restaurantId) === restaurantId);
            if (!allSameRestaurant) {
                console.log('Validation failed: Items from different restaurants');
                return res.status(400).json({
                    success: false,
                    message: 'All items must be from the same restaurant'
                });
            }
        }

        // Verify restaurant exists
        const restaurant = await Restaurant.findByPk(restaurantId);
        if (!restaurant) {
            console.log(`Validation failed: Restaurant ${restaurantId} not found`);
            return res.status(400).json({
                success: false,
                message: 'Restaurant not found'
            });
        }

        // Calculate total price
        let totalPrice = 0;
        for (const item of items) {
            totalPrice += item.price * item.quantity;
        }

        // Add delivery fee
        const deliveryFee = 5.0;
        totalPrice += deliveryFee;
        console.log(`Total price calculated: ${totalPrice} (including delivery fee: ${deliveryFee})`);

        // Create cart
        console.log('Creating cart...');
        const cart = await Cart.create({
            RestaurantID: restaurantId,
            CustomerID: customerId,
            TotalPrice: totalPrice,
            CartDate: Sequelize.fn('GETDATE')
        });
        console.log(`Cart created with ID: ${cart.CartID}`);

        // Create cart items
        console.log('Creating cart items...');
        for (const item of items) {
            console.log(`Adding item: ${item.productName} (ID: ${item.productId}) x${item.quantity}`);

            // Validate product ID
            const productId = parseInt(item.productId);
            if (!productId || productId === 0 || isNaN(productId)) {
                console.log(`Invalid product ID: ${item.productId}`);
                return res.status(400).json({
                    success: false,
                    message: `Invalid product ID: ${item.productId}`
                });
            }

            // Verify product exists
            const product = await Product.findByPk(productId);
            if (!product) {
                console.log(`Product ${productId} not found`);
                return res.status(400).json({
                    success: false,
                    message: `Product not found: ${item.productName}`
                });
            }

            // Validate quantity
            const quantity = parseInt(item.quantity);
            if (!quantity || quantity <= 0 || isNaN(quantity)) {
                console.log(`Invalid quantity: ${item.quantity}`);
                return res.status(400).json({
                    success: false,
                    message: `Invalid quantity for ${item.productName}`
                });
            }

            // Create ProductCart using proper Sequelize model
            let productCart;
            try {
                productCart = await ProductCart.create({
                    CartID: cart.CartID,
                    ProductID: productId,
                    Quantity: quantity
                });
                console.log(`ProductCart created with ID: ${productCart.ProductCartID}, Quantity: ${quantity}`);
            } catch (productCartError) {
                console.error('Error creating ProductCart:', productCartError);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to create cart item',
                    error: productCartError.message
                });
            }

            // Handle ingredients if any
            if (item.selectedIngredients && item.selectedIngredients.length > 0) {
                console.log(`Adding ${item.selectedIngredients.length} ingredients for product ${item.productId}`);
                for (const ingredient of item.selectedIngredients) {
                    // Parse ingredient ID to ensure it's an integer
                    const ingredientId = parseInt(ingredient.id);
                    if (isNaN(ingredientId)) {
                        console.warn(`Invalid ingredient ID: ${ingredient.id}, skipping...`);
                        continue;
                    }

                    try {
                        // Verify ingredient exists and belongs to the product
                        const ingredientExists = await Ingredient.findOne({
                            where: {
                                IngredientID: ingredientId,
                                ProductID: productId
                            }
                        });

                        if (!ingredientExists) {
                            console.warn(`Ingredient ${ingredientId} not found for product ${productId}, skipping...`);
                            continue;
                        }

                        // Create IngredientUsage using proper Sequelize model
                        await IngredientUsage.create({
                            ProductCartID: productCart.ProductCartID,
                            IngredientID: ingredientId,
                            IsNeeded: true,
                            Quantity: ingredient.quantity || 1
                        });
                        console.log(`Added ingredient ${ingredient.name} (ID: ${ingredientId}) with quantity: ${ingredient.quantity || 1}`);
                    } catch (ingredientError) {
                        console.error(`Error adding ingredient ${ingredient.name}:`, ingredientError);
                        // Continue with other ingredients instead of failing the entire order
                    }
                }
            }
        }

        // Get customer location if available
        console.log('Getting customer location...');
        const customer = await Customer.findByPk(customerId);
        let startPointLatitude = null;
        let startPointLongitude = null;

        if (customer && customer.LocationLatitude && customer.LocationLongitude) {
            startPointLatitude = customer.LocationLatitude;
            startPointLongitude = customer.LocationLongitude;
            console.log(`Customer location found: ${startPointLatitude}, ${startPointLongitude}`);
        } else {
            console.log('No customer location found');
        }

        // Create order
        console.log('Creating order...');
        const order = await Order.create({
            DriverID: null, // Will be assigned later
            CartID: cart.CartID,
            CustomerID: customerId,
            StartPointLatitude: startPointLatitude,
            StartPointLongitude: startPointLongitude,
            EndPointLatitude: endPointLatitude || null,
            EndPointLongitude: endPointLongitude || null,
            Duration: null, // Will be calculated when driver is assigned
            OrderDate: Sequelize.fn('GETDATE'),
            Status: 0, // Pending status
            Note: note || null,
            Address: address || null,
            PaymentMethod: paymentMethod || 'cash'
        });
        console.log(`Order created with ID: ${order.OrderID}`);

        console.log('Fetching created order details...');
        const createdOrder = await Order.findByPk(order.OrderID, {
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    include: [
                        {
                            model: ProductCart,
                            as: 'ProductCarts',
                            include: [
                                {
                                    model: Product,
                                    as: 'Product',
                                    attributes: ['ProductName', 'Price', 'Image']
                                }
                            ]
                        }
                    ]
                },
                {
                    model: Customer,
                    as: 'Customer',
                    attributes: ['CustomerID', 'PhoneNum']
                }
            ]
        });

        // Get restaurant details separately
        const restaurantDetails = await Restaurant.findByPk(restaurantId, {
            attributes: ['RestaurantID', 'Name', 'Image']
        });

        console.log('Order created successfully, sending response...');
        res.status(201).json({
            success: true,
            message: 'Order created successfully',
            OrderID: order.OrderID.toString(),
            data: {
                orderId: order.OrderID,
                cartId: cart.CartID,
                totalPrice: totalPrice,
                status: order.Status,
                orderDate: order.OrderDate,
                restaurant: restaurantDetails
            },
            order: createdOrder
        });

    } catch (error) {
        console.error('Error creating order:', error);
        console.error('Error stack:', error.stack);

        // Handle specific error types
        let statusCode = 500;
        let message = 'Failed to create order';

        if (error.name === 'SequelizeValidationError') {
            statusCode = 400;
            message = 'Validation error: ' + error.errors.map(e => e.message).join(', ');
        } else if (error.name === 'SequelizeForeignKeyConstraintError') {
            statusCode = 400;
            message = 'Invalid reference: ' + error.message;
        }

        res.status(statusCode).json({
            success: false,
            message: message,
            error: error.message,
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
};
// Get available orders (Status = 1, no driver assigned)
exports.getAvailableOrders = async(req, res) => {
    try {
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        const orders = await Order.findAndCountAll({
            where: {
                Status: 1,
                DriverID: null
            },
            limit,
            offset,
            order: [['OrderDate', 'DESC']],
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    attributes: ['TotalPrice', 'RestaurantID'],
                },
                {
                    model: Customer,
                    as: 'Customer',
                    attributes: ['FirstName', 'LastName', 'PhoneNumber']
                }
            ]
        });

        const formattedOrders = orders.rows.map(async order => {
            const plainOrder = order.get({ plain: true });

            const {
                StartPointLatitude,
                StartPointLongitude,
                EndPointLatitude,
                EndPointLongitude,
                ...orderData
            } = plainOrder;

            let restaurantName = null;
            let cartTotalPrice = null;

            if (order.CartID) {
                const cart = await Cart.findByPk(order.CartID);
                if (cart) {
                    cartTotalPrice = cart.TotalPrice;
                    if (cart.RestaurantID) {
                        const restaurant = await Restaurant.findByPk(cart.RestaurantID);
                        if (restaurant) {
                            restaurantName = restaurant.Name;
                        }
                    }
                }
            }

            let distanceInMeters = null;
            if (
                StartPointLatitude != null &&
                StartPointLongitude != null &&
                EndPointLatitude != null &&
                EndPointLongitude != null
            ) {
                distanceInMeters = calculateDistance(
                    StartPointLatitude,
                    StartPointLongitude,
                    EndPointLatitude,
                    EndPointLongitude
                );
            }

            return {
                ...orderData,
                RestaurantName: restaurantName,
                CartTotalPrice: cartTotalPrice,
                DistanceInMeters: distanceInMeters
            };
        });

        const resolvedFormattedOrders = await Promise.all(formattedOrders);

        const response = getPagingData({ rows: resolvedFormattedOrders, count: orders.count }, page, limit);
        res.status(200).json(response);

    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching available orders',
            error: error.message
        });
    }
};

// Get customer orders
exports.getCustomerOrders = async(req, res) => {
    try {
        const { customerId } = req.params;
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        const orders = await Order.findAndCountAll({
            where: { CustomerID: customerId },
            limit,
            offset,
            order: [['OrderDate', 'DESC']],
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    attributes: ['TotalPrice', 'CartDate', 'RestaurantID'],
                    include: [
                        {
                            model: ProductCart,
                            as: 'ProductCarts',
                            include: [
                                {
                                    model: Product,
                                    as: 'Product',
                                    attributes: ['ProductID', 'ProductName', 'Price', 'Image']
                                },
                                {
                                    model: IngredientUsage,
                                    as: 'IngredientUsages',
                                    include: [
                                        {
                                            model: Ingredient,
                                            as: 'Ingredient',
                                            attributes: ['IngredientID', 'IngredientName']
                                        }
                                    ],
                                    required: false
                                }
                            ]
                        }
                    ]
                },
                {
                    model: db.Driver,
                    as: 'Driver',
                    attributes: ['FirstName', 'LastName', 'PhoneNumber'],
                    required: false
                }
            ]
        });

        const formattedOrders = await Promise.all(orders.rows.map(async order => {
            const plainOrder = order.get({ plain: true });

            // Format products with ingredients
            const products = plainOrder.Cart?.ProductCarts?.map(pc => ({
                id: pc.Product.ProductID,
                name: pc.Product.ProductName,
                price: pc.Product.Price,
                image: pc.Product.Image,
                quantity: pc.Quantity,
                ingredients: pc.IngredientUsages?.map(iu => ({
                    id: iu.Ingredient.IngredientID,
                    name: iu.Ingredient.IngredientName,
                    isNeeded: iu.IsNeeded
                })) || []
            })) || [];

            // Get restaurant details separately
            let restaurant = null;
            if (plainOrder.Cart?.RestaurantID) {
                try {
                    const restaurantData = await Restaurant.findByPk(plainOrder.Cart.RestaurantID, {
                        attributes: ['RestaurantID', 'Name', 'Image']
                    });
                    if (restaurantData) {
                        restaurant = {
                            id: restaurantData.RestaurantID,
                            name: restaurantData.Name,
                            image: restaurantData.Image
                        };
                    }
                } catch (err) {
                    console.error('Error fetching restaurant:', err);
                }
            }

            // Calculate distance if coordinates are available
            let distanceInMeters = null;
            if (
                plainOrder.StartPointLatitude &&
                plainOrder.StartPointLongitude &&
                plainOrder.EndPointLatitude &&
                plainOrder.EndPointLongitude
            ) {
                distanceInMeters = calculateDistance(
                    plainOrder.StartPointLatitude,
                    plainOrder.StartPointLongitude,
                    plainOrder.EndPointLatitude,
                    plainOrder.EndPointLongitude
                );
            }

            return {
                OrderID: plainOrder.OrderID,
                Status: plainOrder.Status,
                OrderDate: plainOrder.OrderDate,
                TotalPrice: plainOrder.Cart?.TotalPrice || 0,
                Address: plainOrder.Address,
                PaymentMethod: plainOrder.PaymentMethod,
                Note: plainOrder.Note,
                Restaurant: restaurant,
                Driver: plainOrder.Driver ? {
                    name: `${plainOrder.Driver.FirstName} ${plainOrder.Driver.LastName}`.trim(),
                    phone: plainOrder.Driver.PhoneNumber
                } : null,
                Products: products,
                DistanceInMeters: distanceInMeters,
                Location: {
                    startPoint: {
                        latitude: plainOrder.StartPointLatitude,
                        longitude: plainOrder.StartPointLongitude
                    },
                    endPoint: {
                        latitude: plainOrder.EndPointLatitude,
                        longitude: plainOrder.EndPointLongitude
                    }
                }
            };
        }));

        const response = getPagingData({
            rows: formattedOrders,
            count: orders.count
        }, page, limit);

        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching customer orders:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching customer orders',
            error: error.message
        });
    }
};