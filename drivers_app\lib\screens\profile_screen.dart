import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../services/api_client.dart';
import 'login_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  Map<String, dynamic>? _driverData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDriverProfile();
  }

  Future<void> _loadDriverProfile() async {
    try {
      final apiClient = ApiClient();
      // For testing, use driver ID 1. In production, get from auth provider
      final response = await apiClient.get('/drivers/1');

      if (response != null) {
        setState(() {
          _driverData = response;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.purple),
        ),
      );
    }

    if (_driverData == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              "لم يتم تسجيل الدخول",
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => const LoginScreen()),
                );
              },
              child: const Text("تسجيل الدخول"),
            ),
          ],
        ),
      );
    }



    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 16),
          
          // Profile Header
          CircleAvatar(
            radius: 60,
            backgroundColor: Colors.grey[300],
            child: Icon(
              Icons.person,
              size: 80,
              color: Colors.grey[700],
            ),
          ),
          
          SizedBox(height: 16),
          
          Text(
            "${_driverData!['FirstName'] ?? ''} ${_driverData!['LastName'] ?? ''}".trim().isEmpty
                ? "بدون اسم"
                : "${_driverData!['FirstName'] ?? ''} ${_driverData!['LastName'] ?? ''}".trim(),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),

          Text(
            _driverData!['Username'] ?? "بدون اسم مستخدم",
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),

          const SizedBox(height: 8),

          _buildDriverStatus(_driverData!),
          
          SizedBox(height: 24),
          
          _buildInfoCard(
            title: "معلومات شخصية",
            items: [
              InfoItem(
                icon: Icons.phone,
                label: "رقم الهاتف",
                value: _driverData!['PhoneNumber'] ?? "غير متوفر",
              ),
              InfoItem(
                icon: Icons.location_on,
                label: "العنوان",
                value: _driverData!['Address'] ?? "غير متوفر",
              ),
              InfoItem(
                icon: Icons.calendar_today,
                label: "تاريخ الانضمام",
                value: _driverData!['JoinDate'] ?? "غير متوفر",
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          _buildInfoCard(
            title: "معلومات المركبة",
            items: [
              InfoItem(
                icon: Icons.drive_eta,
                label: "نوع المركبة",
                value: _driverData!['CarData'] ?? "غير متوفر",
              ),
              InfoItem(
                icon: Icons.confirmation_number,
                label: "رقم اللوحة",
                value: _driverData!['PlateNumber'] ?? "غير متوفر",
              ),
              InfoItem(
                icon: Icons.directions_car,
                label: "سيارة الشركة",
                value: _driverData!['CompanyCar'] == true ? "نعم" : "لا",
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          _buildInfoCard(
            title: "إحصائيات",
            items: [
              InfoItem(
                icon: Icons.star,
                label: "التقييم",
                value: "غير متوفر", // Rating not available in current data
              ),
              InfoItem(
                icon: Icons.delivery_dining,
                label: "عدد التوصيلات",
                value: "0", // Orders count not available in current data
              ),
              InfoItem(
                icon: Icons.attach_money,
                label: "إجمالي الأرباح",
                value: "${_driverData!['Profit'] ?? 0.0} د.ل",
              ),
            ],
          ),
          
          SizedBox(height: 32),
          
          // Logout Button
          ElevatedButton.icon(
            onPressed: () async {
              await authProvider.logout();
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => LoginScreen()),
              );
            },
            icon: Icon(Icons.logout),
            label: Text("تسجيل الخروج"),
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Colors.red,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              minimumSize: Size(double.infinity, 0),
            ),
          ),
          
          SizedBox(height: 24),
        ],
      ),
    );
  }
  
  Widget _buildDriverStatus(Map<String, dynamic> driverData) {
    Color statusColor;
    String statusText;

    if (driverData['IsActive'] == true) {
      statusColor = Colors.green;
      statusText = "متاح";
    } else {
      statusColor = Colors.red;
      statusText = "غير متاح";
    }
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: statusColor),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.circle,
            size: 12,
            color: statusColor,
          ),
          SizedBox(width: 6),
          Text(
            statusText,
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildInfoCard({required String title, required List<InfoItem> items}) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            SizedBox(height: 12),
            ...items.map((item) => Column(
              children: [
                Row(
                  children: [
                    Icon(
                      item.icon,
                      color: Colors.grey[600],
                      size: 22,
                    ),
                    SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.label,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(height: 2),
                        Text(
                          item.value,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                if (items.last != item)
                  Divider(height: 24),
              ],
            )).toList(),
          ],
        ),
      ),
    );
  }
}

class InfoItem {
  final IconData icon;
  final String label;
  final String value;
  
  const InfoItem({
    required this.icon,
    required this.label,
    required this.value,
  });
} 