const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');

router.get('/all', orderController.getAllOrders);
router.get('/available', orderController.getAvailableOrders);
router.get('/customer/:customerId', orderController.getCustomerOrders);
router.get('/details/:orderId', orderController.getOrderDetails);
router.get('/cart/:orderId', orderController.getOrderCart);
router.get('/restaurant/:restaurantId', orderController.getRestaurantOrders);
router.get('/driver/:driverId', orderController.getDriverOrders);
router.post('/create', orderController.createOrder);
router.put('/:orderId', orderController.editOrder);

module.exports = router;
